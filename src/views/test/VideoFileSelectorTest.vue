<template>
  <div class="test-page">
    <el-card header="视频文件选择组件测试">
      <div class="test-section">
        <h3>基本使用</h3>
        <div class="form-item">
          <label>选择的文件ID：</label>
          <span>{{ selectedFileId || '未选择' }}</span>
        </div>
        
        <div class="form-item">
          <label>视频文件选择器：</label>
          <VideoFileSelector
            v-model="selectedFileId"
            @change="handleFileChange"
            :max-size="1000"
            accept-types=".mp4,.avi,.mov,.flv,.wmv,.mkv,.m4v"
          />
        </div>
        
        <div class="form-item" v-if="selectedFileInfo">
          <label>选择的文件信息：</label>
          <pre>{{ JSON.stringify(selectedFileInfo, null, 2) }}</pre>
        </div>
      </div>
      
      <div class="test-section">
        <h3>表单集成测试</h3>
        <el-form :model="testForm" :rules="testRules" ref="testFormRef" label-width="120px">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="testForm.taskName" placeholder="请输入任务名称" />
          </el-form-item>
          
          <el-form-item label="视频文件" prop="videoFileId">
            <VideoFileSelector
              v-model="testForm.videoFileId"
              @change="handleFormFileChange"
            />
          </el-form-item>
          
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="testForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'

export default {
  name: 'VideoFileSelectorTest',
  components: {
    VideoFileSelector
  },
  data() {
    return {
      selectedFileId: '',
      selectedFileInfo: null,
      
      testForm: {
        taskName: '',
        videoFileId: '',
        description: ''
      },
      
      testRules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        videoFileId: [
          { required: true, message: '请选择视频文件', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleFileChange(fileId, fileInfo) {
      console.log('文件选择变化:', fileId, fileInfo)
      this.selectedFileInfo = fileInfo
    },
    
    handleFormFileChange(fileId, fileInfo) {
      console.log('表单文件选择变化:', fileId, fileInfo)
    },
    
    submitForm() {
      this.$refs.testFormRef.validate((valid) => {
        if (valid) {
          console.log('表单提交:', this.testForm)
          this.$message.success('表单验证通过！')
        } else {
          this.$message.error('表单验证失败！')
          return false
        }
      })
    },
    
    resetForm() {
      this.$refs.testFormRef.resetFields()
      this.selectedFileId = ''
      this.selectedFileInfo = null
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 20px;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: inline-block;
  width: 120px;
  font-weight: 500;
  color: #606266;
}

.form-item pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
