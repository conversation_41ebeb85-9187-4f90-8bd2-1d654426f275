# VideoFileSelector 视频文件选择组件

## 功能描述

VideoFileSelector 是一个可复用的视频文件选择组件，支持两种方式选择视频文件：

1. **从现有文件列表中选择** - 通过调用 `system/AutoOsmotic/list` 接口获取视频文件列表
2. **直接上传新文件** - 支持拖拽上传或点击上传，上传成功后自动添加到文件列表

## 特性

- ✅ 支持 v-model 双向绑定
- ✅ 文件列表搜索和分页
- ✅ 拖拽上传和点击上传
- ✅ 文件类型和大小验证
- ✅ 上传进度显示
- ✅ 自动选择刚上传的文件
- ✅ 响应式设计，适配不同屏幕尺寸

## 使用方法

### 基本使用

```vue
<template>
  <VideoFileSelector
    v-model="selectedFileId"
    @change="handleFileChange"
  />
</template>

<script>
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'

export default {
  components: {
    VideoFileSelector
  },
  data() {
    return {
      selectedFileId: ''
    }
  },
  methods: {
    handleFileChange(fileId, fileInfo) {
      console.log('选择的文件:', fileId, fileInfo)
    }
  }
}
</script>
```

### 在 EleSheet 表单中使用

```vue
<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps">
    <!-- 自定义视频文件选择字段 -->
    <template #value1:simple="{ model, field }">
      <VideoFileSelector
        v-model="model[field]"
        @change="handleVideoFileChange"
      />
    </template>
  </EleSheet>
</template>

<script>
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'

export default {
  components: {
    VideoFileSelector
  },
  computed: {
    sheetProps() {
      return {
        model: {
          value1: {
            type: 'text',
            label: '视频文件',
            form: {
              type: 'custom',
              rules: [
                { required: true, message: '请选择视频文件', trigger: 'change' }
              ]
            }
          }
        }
      }
    }
  },
  methods: {
    handleVideoFileChange(fileId, fileInfo) {
      console.log('选择的视频文件:', fileId, fileInfo)
    }
  }
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 选中的文件ID，支持 v-model | String/Number | '' |
| acceptTypes | 接受的文件类型 | String | '.mp4,.avi,.mov,.flv,.wmv,.mkv' |
| maxSize | 最大文件大小(MB) | Number | 500 |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| input | 文件选择变化时触发 | (fileId) |
| change | 文件选择变化时触发 | (fileId, fileInfo) |

## 数据结构

### 文件信息对象 (fileInfo)

```javascript
{
  id: "文件唯一标识",
  value1: "任务编号",
  value3: "视频文件名",
  value4: "文件存储路径",
  value5: "文件大小",
  value6: "文件格式",
  value7: "文件状态",
  value8: "采集来源",
  value9: "文件生成时间",
  // ... 其他字段
}
```

## API 接口

### 获取文件列表

- **接口**: `GET /system/AutoOsmotic/list`
- **参数**: 
  - `type`: 'video_file_management'
  - `pageNum`: 页码
  - `pageSize`: 每页数量
  - `value3`: 文件名搜索关键词

### 上传文件

- **接口**: `POST /common/upload`
- **说明**: 上传成功后会自动调用 `/system/AutoOsmotic` 接口创建文件记录

### 创建文件记录

- **接口**: `POST /system/AutoOsmotic`
- **参数**: 
  - `type`: 'video_file_management'
  - `value3`: 文件名
  - `value4`: 文件路径
  - `value5`: 文件大小
  - `value6`: 文件格式
  - `value7`: 文件状态

## 样式定制

组件使用了 Element UI 的样式系统，可以通过以下 CSS 类进行样式定制：

```css
.video-file-selector {
  /* 组件容器 */
}

.selector-tabs {
  /* 标签页容器 */
}

.file-item {
  /* 文件项样式 */
}

.file-item.selected {
  /* 选中的文件项样式 */
}

.selected-file-info {
  /* 已选择文件信息样式 */
}
```

## 注意事项

1. 组件依赖 Element UI，确保项目中已正确安装和配置
2. 上传功能需要后端支持 `/common/upload` 接口
3. 文件列表功能需要后端支持 `/system/AutoOsmotic/list` 接口
4. 组件会自动处理文件上传后的记录创建，确保后端接口返回正确的数据格式
5. 建议在使用前测试上传和文件列表功能是否正常工作

## 更新日志

### v1.0.0
- 初始版本
- 支持文件列表选择和文件上传
- 支持 v-model 双向绑定
- 支持搜索和分页
- 支持文件类型和大小验证
