<template>
  <div class="video-file-selector">
    <el-tabs v-model="activeTab" type="default" class="selector-tabs">
      <!-- 从现有文件列表选择 -->
      <el-tab-pane label="选择现有文件" name="select">
        <div class="file-list-container">
          <!-- 搜索框 -->
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入文件名搜索"
              prefix-icon="el-icon-search"
              clearable
              @input="handleSearch"
            />
          </div>

          <!-- 文件列表 -->
          <div v-loading="loading" class="file-list">
            <el-radio-group v-model="selectedFileId" @change="handleFileSelect">
              <div class="file-item-container">
                <div
                  v-for="file in filteredFileList"
                  :key="file.id"
                  class="file-item"
                  :class="{ 'selected': selectedFileId === file.id }"
                >
                  <el-radio :label="file.id" class="file-radio">
                    <div class="file-info">
                      <div class="file-name">{{ file.value3 || '未命名文件' }}</div>
                      <div class="file-details">
                        <span class="file-size">{{ formatFileSize(file.value5) }}</span>
                        <el-tag size="mini" type="primary">{{ file.value6 || 'Unknown' }}</el-tag>
                        <el-tag size="mini" :type="getStatusType(file.value7)">{{ file.value7 || '未知' }}</el-tag>
                      </div>
                      <div v-if="file.value4" class="file-path">{{ file.value4 }}</div>
                    </div>
                  </el-radio>
                </div>
              </div>
            </el-radio-group>

            <!-- 空状态 -->
            <div v-if="!loading && filteredFileList.length === 0" class="empty-state">
              <i class="el-icon-document"></i>
              <p>{{ searchKeyword ? '未找到匹配的文件' : '暂无视频文件' }}</p>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="total > pageSize" class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 上传新文件 -->
      <el-tab-pane label="上传新文件" name="upload">
        <div class="upload-container">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="uploadFileList"
            :accept="acceptTypes"
            :limit="1"
            :on-exceed="handleExceed"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">
              支持 {{ acceptTypes }} 格式，单个文件大小不超过 {{ maxSize }}MB
            </div>
          </el-upload>

          <!-- 上传进度 -->
          <div v-if="uploading" class="upload-progress">
            <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 已选择的文件信息 -->
    <div v-if="selectedFile" class="selected-file-info">
      <div class="selected-label">已选择文件：</div>
      <div class="selected-file">
        <i class="el-icon-video-camera"></i>
        <span class="file-name">{{ selectedFile.value3 }}</span>
        <el-tag size="mini" type="success">{{ selectedFile.value6 }}</el-tag>
        <el-button type="text" size="mini" @click="clearSelection">清除</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import { getToken } from '@/utils/auth'

export default {
  name: 'VideoFileSelector',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 接受的文件类型
    acceptTypes: {
      type: String,
      default: '.mp4,.avi,.mov,.flv,.wmv,.mkv'
    },
    // 最大文件大小(MB)
    maxSize: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      activeTab: 'select',
      loading: false,
      fileList: [],
      filteredFileList: [],
      searchKeyword: '',
      selectedFileId: this.value,

      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 上传相关
      uploading: false,
      uploadProgress: 0,
      uploadStatus: '',
      uploadFileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },

      // 搜索防抖
      searchTimer: null
    }
  },
  computed: {
    selectedFile() {
      return this.fileList.find(file => file.id === this.selectedFileId)
    }
  },
  watch: {
    value(newVal) {
      this.selectedFileId = newVal
    },
    selectedFileId(newVal) {
      this.$emit('input', newVal)
      this.$emit('change', newVal, this.selectedFile)
    }
  },
  mounted() {
    this.loadFileList()
  },
  methods: {
    // 加载文件列表
    async loadFileList() {
      this.loading = true
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          type: 'video_file_management'
        }

        if (this.searchKeyword) {
          params.value3 = this.searchKeyword
        }

        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params
        })

        if (response.code === 200) {
          this.fileList = response.rows || []
          this.total = response.total || 0
          this.filterFileList()
        }
      } catch (error) {
        console.error('加载文件列表失败:', error)
        this.$message.error('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },

    // 过滤文件列表
    filterFileList() {
      this.filteredFileList = this.fileList
    },

    // 搜索处理
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1
        this.loadFileList()
      }, 300)
    },

    // 文件选择
    handleFileSelect(fileId) {
      this.selectedFileId = fileId
    },

    // 清除选择
    clearSelection() {
      this.selectedFileId = ''
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadFileList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadFileList()
    },

    // 上传前验证
    handleBeforeUpload(file) {
      // 验证文件类型
      const fileExt = '.' + file.name.split('.').pop().toLowerCase()
      const acceptList = this.acceptTypes.split(',')
      if (!acceptList.includes(fileExt)) {
        this.$message.error(`不支持的文件格式，请上传 ${this.acceptTypes} 格式的文件`)
        return false
      }

      // 验证文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB`)
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      this.uploadStatus = ''

      return true
    },

    // 上传成功
    async handleUploadSuccess(response, file) {
      this.uploading = false
      this.uploadProgress = 100
      this.uploadStatus = 'success'

      if (response.code === 200) {
        this.$message.success('文件上传成功')

        // 创建文件记录
        try {
          const fileData = {
            type: 'video_file_management',
            value3: file.name,
            value4: response.url || response.fileName,
            value5: this.formatFileSize(file.size),
            value6: file.name.split('.').pop().toUpperCase(),
            value7: '正常',
            value9: new Date().toISOString().slice(0, 19).replace('T', ' ')
          }

          const addResponse = await request({
            url: '/system/AutoOsmotic',
            method: 'post',
            data: fileData
          })

          if (addResponse.code === 200) {
            // 重新加载文件列表
            await this.loadFileList()

            // 自动选择刚上传的文件
            const newFile = this.fileList.find(f => f.value3 === file.name)
            if (newFile) {
              this.selectedFileId = newFile.id
              this.activeTab = 'select'
            }
          }
        } catch (error) {
          console.error('创建文件记录失败:', error)
          this.$message.warning('文件上传成功，但创建记录失败')
        }

        this.uploadFileList = []
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 上传失败
    handleUploadError(err) {
      this.uploading = false
      this.uploadStatus = 'exception'
      this.$message.error('文件上传失败')
      console.error('上传错误:', err)
    },

    // 文件数量超出限制
    handleExceed() {
      this.$message.warning('只能上传一个文件')
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      if (typeof size === 'string') return size

      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '正常': 'success',
        '异常': 'danger',
        '处理中': 'warning',
        '已删除': 'info'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.video-file-selector {
  width: 100%;
}

.selector-tabs {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.file-list-container {
  padding: 16px;
}

.search-bar {
  margin-bottom: 16px;
}

.file-list {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.file-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 12px;
  transition: all 0.3s;
}

.file-item:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.file-item.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.file-radio {
  width: 100%;
}

.file-info {
  margin-left: 20px;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.file-path {
  color: #909399;
  font-size: 12px;
  word-break: break-all;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  text-align: center;
}

.upload-container {
  padding: 16px;
}

.upload-dragger {
  width: 100%;
}

.upload-progress {
  margin-top: 16px;
}

.selected-file-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.selected-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selected-file .file-name {
  font-weight: 500;
  flex: 1;
}
</style>
